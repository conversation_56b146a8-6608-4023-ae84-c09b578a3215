# outputs.tf
#output "gke_cluster_name" {
#  description = "GKE cluster name"
#  value       = module.gke.cluster_name
#}

#output "gke_cluster_endpoint" {
#  description = "GKE cluster endpoint"
#  value       = module.gke.cluster_endpoint
#  sensitive   = true
#}

output "artifact_registry_repositories" {
  description = "Artifact Registry repository URLs"
  value       = module.artifact_registry.repository_urls
}

output "cloudsql_instance_name" {
  description = "CloudSQL instance name"
  value       = module.cloudsql.instance_name
}

output "cloudsql_connection_name" {
  description = "CloudSQL connection name"
  value       = module.cloudsql.connection_name
}

output "workload_identity_provider" {
  description = "Workload Identity Provider for GitHub Actions"
  value       = module.workload_identity.provider_name
}

output "workload_identity_service_account" {
  description = "Service Account for GitHub Actions"
  value       = module.workload_identity.service_account_email
}

output "vpc_network_name" {
  description = "VPC network name"
  value       = google_compute_network.main.name
}

output "cloud_run_service_account" {
  description = "Service account email for Cloud Run"
  value       = module.cloud_run.service_account_email
}

output "cloud_run_config" {
  description = "Configuration values for Cloud Run deployment"
  value = {
    service_account_email = module.cloud_run.service_account_email
    vpc_connector        = google_vpc_access_connector.connector.id
    vpc_connector_name   = google_vpc_access_connector.connector.name
    project_id           = var.project_id
    region               = var.region
  }
}

output "cr_yaml_snippet" {
  description = "Ready-to-use snippet for cr.yaml"
  value = <<-EOT
    # Add to your cr.yaml:
    spec:
      template:
        metadata:
          annotations:
            run.googleapis.com/vpc-access-connector: ${google_vpc_access_connector.connector.id}
            run.googleapis.com/vpc-access-egress: PRIVATE_RANGES_ONLY
        spec:
          serviceAccountName: ${module.cloud_run.service_account_email}
  EOT
}

output "github_actions_secrets" {
  description = "Values to add as GitHub Actions secrets"
  value = <<-EOT
    Add these as GitHub Secrets:
    - GCP_PROJECT_ID: ${var.project_id}
    - GCP_WIF_PROVIDER: ${module.workload_identity.provider_name}
    - GCP_WIF_SERVICE_ACCOUNT: ${module.workload_identity.service_account_email}
  EOT
}