# Terraform .gitignore

### Terraform Files ###
# Local .terraform directories
**/.terraform/*

# .tfstate files - CRITICAL: Never commit state files!
*.tfstate
*.tfstate.*

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data
#*.tfvars
#*.tfvars.json

# Ignore override files as they are usually used to override resources locally
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
*tfplan*

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Terraform lock file (you may want to commit this - see note below)
.terraform.lock.hcl

### Sensitive Files ###
# Private keys
*.pem
*.key
*.pfx
*.p12

# GCP Service Account keys
*-key.json
*-credentials.json
service-account-*.json
gcp-key.json

# AWS credentials
.aws/
aws-credentials

# Azure credentials
azure-credentials.json

# Environment variables file
.env
.env.*
!.env.example

### Backup Files ###
*.backup
*.bak
*.tmp
*.temp
*~

### OS Files ###
# macOS
.DS_Store
.AppleDouble
.LSOverride
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
.directory
.Trash-*

### Editor/IDE Files ###
# VSCode
.vscode/
*.code-workspace
.history/

# IntelliJ IDEA
.idea/
*.iml
*.iws
*.ipr
out/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*.swn
.vim/

# Emacs
*~
\#*\#
.\#*

### Python (for any scripts) ###
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/

### Node (for any tooling) ###
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

### Project Specific ###
# Temporary test files
test/
tests/
*.test.tf
*.test.tfvars

# Local scripts that shouldn't be shared
local-scripts/
local/

# Documentation build artifacts
docs/_build/
site/

# Logs
logs/
*.log

# Certificates (except example/template certs)
*.crt
*.cer
!example.crt

# Kubernetes configs
kubeconfig
kubeconfig.*
.kube/

# Ansible
*.retry

# Packer
packer_cache/

